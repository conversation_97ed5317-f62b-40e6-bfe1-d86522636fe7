const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1/facultyauth';

async function testFacultyAuth() {
  console.log('🧪 Testing Faculty Authentication API...\n');

  try {
    let facultyId = null;

    // Test 1: Register Faculty
    console.log('1️⃣ Testing Faculty Registration...');
    const registerData = {
      fullName: 'Dr. <PERSON>',
      email: '<EMAIL>',
      phoneNumber: '+639123456789',
      password: 'FacultyPass123',
      department: 'Computer Science',
      position: 'Professor',
      status: 'Active'
    };

    const registerResponse = await axios.post(`${BASE_URL}/register-faculty`, registerData);
    console.log('✅ Faculty registered:', registerResponse.data);
    facultyId = registerResponse.data.data.facultyID;

    // Test 2: Faculty Login
    console.log('\n2️⃣ Testing Faculty Login...');
    const loginData = {
      facultyId: '2024-00001', // Assuming this maps to facultyId 1
      password: 'FacultyPass123'
    };

    const loginResponse = await axios.post(`${BASE_URL}/login-faculty`, loginData);
    console.log('✅ Faculty login successful:', loginResponse.data);

    // Test 3: Get Faculty by ID
    console.log('\n3️⃣ Testing Get Faculty by ID...');
    const getFacultyResponse = await axios.get(`${BASE_URL}/get-faculty/${facultyId}`);
    console.log('✅ Faculty retrieved:', getFacultyResponse.data);

    // Test 4: Get All Faculty
    console.log('\n4️⃣ Testing Get All Faculty...');
    const getAllFacultyResponse = await axios.get(`${BASE_URL}/get-all-faculty`);
    console.log('✅ All faculty retrieved:', getAllFacultyResponse.data);

    // Test 5: Get Faculty by Department
    console.log('\n5️⃣ Testing Get Faculty by Department...');
    const getDepartmentResponse = await axios.get(`${BASE_URL}/get-faculty-by-department/Computer Science`);
    console.log('✅ Faculty by department retrieved:', getDepartmentResponse.data);

    // Test 6: Get Faculty by Position
    console.log('\n6️⃣ Testing Get Faculty by Position...');
    const getPositionResponse = await axios.get(`${BASE_URL}/get-faculty-by-position/Professor`);
    console.log('✅ Faculty by position retrieved:', getPositionResponse.data);

    // Test 7: Update Faculty
    console.log('\n7️⃣ Testing Faculty Update...');
    const updateData = {
      fullName: 'Dr. Maria Santos-Updated',
      email: '<EMAIL>',
      phoneNumber: '+639987654321',
      department: 'Information Technology',
      position: 'Associate Professor'
    };

    const updateResponse = await axios.put(`${BASE_URL}/update-faculty/${facultyId}`, updateData);
    console.log('✅ Faculty updated:', updateResponse.data);

    // Test 8: Change Password
    console.log('\n8️⃣ Testing Password Change...');
    const passwordData = {
      currentPassword: 'FacultyPass123',
      newPassword: 'NewFacultyPass456'
    };

    const changePasswordResponse = await axios.post(`${BASE_URL}/change-faculty-password/${facultyId}`, passwordData);
    console.log('✅ Password changed:', changePasswordResponse.data);

    // Test 9: Get Audit Logs
    console.log('\n9️⃣ Testing Get Audit Logs...');
    const auditLogsResponse = await axios.get(`${BASE_URL}/faculty-audit-logs`);
    console.log('✅ Audit logs retrieved:', auditLogsResponse.data);

    // Test 10: Get Audit Logs for specific faculty
    console.log('\n🔟 Testing Get Audit Logs for Specific Faculty...');
    const specificAuditResponse = await axios.get(`${BASE_URL}/faculty-audit-logs/${facultyId}`);
    console.log('✅ Specific faculty audit logs:', specificAuditResponse.data);

    // Test 11: Register Another Faculty for Testing
    console.log('\n1️⃣1️⃣ Testing Register Another Faculty...');
    const registerData2 = {
      fullName: 'Prof. John Doe',
      email: '<EMAIL>',
      phoneNumber: '+639876543210',
      password: 'JohnPass123',
      department: 'Mathematics',
      position: 'Assistant Professor',
      status: 'Active'
    };

    const registerResponse2 = await axios.post(`${BASE_URL}/register-faculty`, registerData2);
    console.log('✅ Second faculty registered:', registerResponse2.data);
    const facultyId2 = registerResponse2.data.data.facultyID;

    // Test 12: Delete Faculty
    console.log('\n1️⃣2️⃣ Testing Faculty Deletion...');
    const deleteResponse = await axios.delete(`${BASE_URL}/delete-faculty/${facultyId2}`);
    console.log('✅ Faculty deleted:', deleteResponse.data);

    console.log('\n🎉 All faculty authentication tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 404) {
      console.log('\n💡 Note: Make sure the facultyauditlogs table exists in your database.');
      console.log('   Run the updated dblibrary.sql file to create the missing table.');
    }
  }
}

// Run the tests
testFacultyAuth();
